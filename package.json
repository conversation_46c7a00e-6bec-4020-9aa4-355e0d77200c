{"name": "pml-widget-eq", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "webpack serve --open --mode development --env env=dev", "build-desktop": "webpack --env desktop env=prod addons=fedration", "serve": "node server.js", "build-mobile": "cross-env NODE_ENV=staging webpack --env env=prod", "build-mobile-beta": "cross-env NODE_ENV=beta webpack --env env=prod release", "build-mobile-prod": "cross-env NODE_ENV=production webpack --env env=prod release", "build": "npm run build-mobile", "build-beta": "npm run build-mobile-beta", "build-prod": "npm run build-mobile-prod", "build:bundleanalyzer": "npm run build-prod addons=bundleanalyzer", "format": "prettier --write .", "format:check": "prettier --check .", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "author": "", "license": "ISC", "dependencies": {"@paytm-money/utils-frontend": "0.1.81-pmlWidgetUtils", "@tanstack/react-query": "^4.33.0", "axios": "^1.7.9", "chart.js": "^4.4.9", "crypto-js": "^4.2.0", "eslint-config-react-app": "^7.0.1", "highcharts": "^12.3.0", "highcharts-react-official": "^3.2.2", "lightweight-charts": "^5.0.2", "lottie-react": "^2.4.0", "process": "^0.11.10", "prop-types": "^15.8.1", "query-string": "^9.1.0", "react": "^16.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^16.2.0", "react-router-dom": "^6.11.2", "react-slick": "^0.30.3", "react-swipeable": "^7.0.2", "rxjs": "^6.5.3", "slick-carousel": "^1.8.1"}, "devDependencies": {"@babel/eslint-parser": "^7.24.5", "@babel/plugin-transform-runtime": "^7.24.3", "@babel/preset-react": "^7.24.1", "@eslint/js": "^9.20.0", "@tanstack/react-query-devtools": "^4.33.0", "babel-loader": "^9.1.3", "compression-webpack-plugin": "^11.1.0", "copy-webpack-plugin": "^12.0.2", "cross-env": "^7.0.3", "css-loader": "^5.1.1", "css-minimizer-webpack-plugin": "^6.0.0", "dotenv-webpack": "^8.1.0", "eslint": "^8.57.1", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^8.9.0", "eslint-config-react-app": "^7.0.1", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-css-modules": "^2.11.0", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-import": "^2.28.0", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^4.6.0", "express-static-gzip": "^2.1.7", "html-webpack-plugin": "^5.6.0", "mini-css-extract-plugin": "^2.9.0", "prettier": "^3.2.5", "sass": "^1.68.0", "sass-loader": "^14.2.1", "sass-resources-loader": "^2.2.3", "string-replace-loader": "^3.1.0", "style-loader": "^3.3.0", "stylelint-config-prettier": "^9.0.5", "webpack": "^5.91.0", "webpack-assets-manifest": "^5.1.0", "webpack-bundle-analyzer": "^4.4.0", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.3.1", "webpack-merge": "^5.7.3", "workbox-webpack-plugin": "^7.0.0", "worker-loader": "^3.0.8"}}
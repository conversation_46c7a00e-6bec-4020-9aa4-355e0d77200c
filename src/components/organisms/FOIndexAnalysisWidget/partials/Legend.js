import cx from 'classnames';
import styles from './index.scss';
import Icon from '../../../molecules/Icon';
import { CHART_TYPES } from '../enums';
import { isDarkMode } from '../../../../utils/commonUtil';

const Legend = ({
  expiryDates,
  selectedExpiry,
  handleExpiryChange,
  selectedTab,
  chartType,
  onChartTypeChange,
  selectedPeriod,
  onPeriodChange,
  periodTypes = [],
  label = '',
}) => {
  const getCallOIColor = () => {
    const color = isDarkMode() ? '#B74040' : '#EB4B4B';
    return {
      backgroundColor: color,
      color,
    };
  };
  const getPutOIColor = () => {
    const color = isDarkMode() ? '#02A85D' : '#2CB079';
    return {
      backgroundColor: color,
      color,
    };
  };

  const getPriceColor = () => {
    const color = isDarkMode() ? '#02A85D' : '#2CB079';
    return {
      backgroundColor: color,
      color,
    };
  };
  const getATMStraddleColor = () => {
    const color = isDarkMode() ? '#0A86BF' : '#013DA6';
    return {
      backgroundColor: color,
      color,
    };
  };
  const getMaxPainColor = () => {
    const color = isDarkMode() ? '#0A86BF' : '#013DA6';
    return {
      backgroundColor: color,
      color,
    };
  };

  const renderLegendItems = () => {
    let nextChartType;

    switch (selectedTab) {
      case 'change-in-oi':
      case 'open-interest':
        return (
          <div className={styles.legend}>
            {expiryDates && (
              <div className={styles.expirySelector}>
                {expiryDates.map((expiry) => (
                  <button
                    type="button"
                    key={expiry.key}
                    className={`${styles.expiryButton} ${selectedExpiry === expiry.key ? styles.activeExpiry : ''}`}
                    onClick={() => handleExpiryChange(expiry.key)}
                  >
                    {expiry.label}
                  </button>
                ))}
              </div>
            )}
            <div className={styles.legendDiv}>
              <div
                className={styles.legendItem}
                style={{ color: getCallOIColor().color }}
              >
                <div
                  className={styles.legendDot}
                  style={{ backgroundColor: getCallOIColor().backgroundColor }}
                />
                <span>Calls OI</span>
              </div>
              <div
                className={styles.legendItem}
                style={{ color: getPutOIColor().color }}
              >
                <div
                  className={styles.legendDot}
                  style={{ backgroundColor: getPutOIColor().backgroundColor }}
                />
                <span>Puts OI</span>
              </div>
            </div>
          </div>
        );
      case 'price-chart':
        nextChartType = Object.values(CHART_TYPES).find(
          (type) => type.key !== chartType,
        );
        return (
          <div className={styles.chartTypeSwitch}>
            <div
              className={styles.switchButton}
              onClick={() => onChartTypeChange(nextChartType.key)}
            >
              <Icon name={nextChartType.icon} width={16} height={16} />
              <span>{nextChartType.label}</span>
            </div>
          </div>
        );
      case 'fii-dii':
        return (
          <div className={styles.legend}>
            <div className={styles.expirySelector}>
              {periodTypes.map((period) => (
                <button
                  type="button"
                  key={period.key}
                  className={`${styles.expiryButton} ${selectedPeriod === period.key ? styles.activeExpiry : ''}`}
                  onClick={() => onPeriodChange(period.key)}
                >
                  {period.label}
                </button>
              ))}
            </div>
            <div className={styles.legendDiv}>
              <div
                className={styles.legendItem}
                style={{ color: getCallOIColor().color }}
              >
                <div
                  className={styles.legendDot}
                  style={{ backgroundColor: getCallOIColor().backgroundColor }}
                />
                <span>FII</span>
              </div>
              <div
                className={styles.legendItem}
                style={{ color: getPutOIColor().color }}
              >
                <div
                  className={styles.legendDot}
                  style={{ backgroundColor: getPutOIColor().backgroundColor }}
                />
                <span>DII</span>
              </div>
            </div>
          </div>
        );
      case 'atm-straddle':
      case 'pcr':
        return (
          <>
            <div
              className={styles.legendItem}
              style={{ color: getPriceColor().color }}
            >
              <div
                className={styles.legendDot}
                style={{ backgroundColor: getPriceColor().backgroundColor }}
              />
              <span>Price</span>
            </div>
            <div
              className={styles.legendItem}
              style={{ color: getATMStraddleColor().color }}
            >
              <div
                className={styles.legendDot}
                style={{
                  backgroundColor: getATMStraddleColor().backgroundColor,
                }}
              />
              <span>{`${selectedTab === 'atm-straddle' ? 'ATM Straddle:' : 'PCR:'} ${label}`}</span>
            </div>
          </>
        );
      case 'advance-decline':
        return (
          <>
            <div
              className={styles.legendItem}
              style={{ color: getPriceColor().color }}
            >
              <div
                className={styles.legendDot}
                style={{ backgroundColor: getPriceColor().backgroundColor }}
              />
              <span>Advance</span>
            </div>
            <div
              className={styles.legendItem}
              style={{ color: getATMStraddleColor().color }}
            >
              <div
                className={styles.legendDot}
                style={{
                  backgroundColor: getATMStraddleColor().backgroundColor,
                }}
              />
              <span>Decline</span>
            </div>
          </>
        );
      case 'max-pain':
        return (
          <>
            <div
              className={styles.legendItem}
              style={{ color: getPriceColor().color }}
            >
              <div
                className={styles.legendDot}
                style={{ backgroundColor: getPriceColor().backgroundColor }}
              />
              <span>Price</span>
            </div>
            <div
              className={styles.legendItem}
              style={{ color: getMaxPainColor().color }}
            >
              <div
                className={styles.legendDot}
                style={{
                  backgroundColor: getMaxPainColor().backgroundColor,
                }}
              />
              <span>Max Pain</span>
            </div>
          </>
        );

      default:
        return null;
    }
  };

  return (
    <div
      className={cx(styles.controlsRow, {
        [styles.controlsRowOverlapChart]: selectedTab === 'price-chart',
      })}
    >
      {renderLegendItems()}
    </div>
  );
};

export default Legend;

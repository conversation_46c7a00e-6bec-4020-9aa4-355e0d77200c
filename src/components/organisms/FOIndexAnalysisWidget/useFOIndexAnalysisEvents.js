import { useCallback } from 'react';
import { useAnalyticsEventForWidget } from '../../../hooks/analyticsHooks';
import { EVENT, EVENT_ACTION, EVENT_CATGEORY, VERTICAL_NAME } from './enums';
import { getUserId } from '../../../utils/coreUtil';

const useFOIndexAnalysisEvents = () => {
  const { sendAnalyticsEventWidget } = useAnalyticsEventForWidget();

  const handleEvents = useCallback(
    (params) => {
      sendAnalyticsEventWidget({
        event: EVENT.CUSTOM_EVENT,
        category: EVENT_CATGEORY,
        verticalName: VERTICAL_NAME,
        user_id: getUserId(),
        label5: 'trader_mode',
        ...params,
      });
    },
    [sendAnalyticsEventWidget],
  );

  const foEventWidgetLoaded = useCallback(
    (indexSymbol) => {
      handleEvents({
        event: EVENT.OPEN_SCREEN,
        action: EVENT_ACTION.FNO_INDEX_WIDGET_LOADED,
        label2: indexSymbol,
      });
    },
    [handleEvents],
  );

  const foEventTabSelected = useCallback(
    (tabName, indexSymbol) => {
      handleEvents({
        action: EVENT_ACTION.FNO_INDEX_WIDGET_TAB_SELECTED,
        label: tabName,
        label2: indexSymbol,
      });
    },
    [handleEvents],
  );

  const foEventCTAClicked = useCallback(
    (ctaName, indexSymbol) => {
      handleEvents({
        action: EVENT_ACTION.CTA_CLICKED,
        label: ctaName,
        label2: indexSymbol,
      });
    },
    [handleEvents],
  );

  const foEventIndexDropdownClicked = useCallback(
    (indexSymbol) => {
      handleEvents({
        action: EVENT_ACTION.INDEX_DROPDOWN_CLICKED,
        label2: indexSymbol,
      });
    },
    [handleEvents],
  );

  const foEventIndexSelected = useCallback(
    (indexSymbol) => {
      handleEvents({
        action: EVENT_ACTION.INDEX_SELECTED,
        label2: indexSymbol,
      });
    },
    [handleEvents],
  );

  return {
    foEventWidgetLoaded,
    foEventTabSelected,
    foEventCTAClicked,
    foEventIndexDropdownClicked,
    foEventIndexSelected,
  };
};

export default useFOIndexAnalysisEvents;

# HighchartsBarChart Component

A React component that renders a bar chart using Highcharts with the following features:

## Features

1. **Sticky Right Y-axis**: Y-axis is positioned on the right side of the chart
2. **Auto-scroll functionality**: Can automatically scroll to a specific X-axis point on mount
3. **Grouped bars**: Shows two data series (Calls OI and Puts OI) as grouped bars
4. **Dark/Light theme support**: Automatically adapts colors based on the current theme
5. **Responsive design**: Adapts to different screen sizes
6. **Custom Y-axis formatting**: Supports custom formatting functions for Y-axis labels

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `data` | Object | `null` | Chart data object with `labels`, `callsOI`, and `putsOI` arrays |
| `height` | Number | `200` | Height of the chart in pixels |
| `config` | Object | `null` | Configuration object for datasets (colors, labels, keys) |
| `scrollToIndex` | Number | `null` | Index to scroll to on mount. If null, auto-scrolls to end |
| `yAxisFormatter` | Function | `(value) => value` | Function to format Y-axis labels |

## Data Format

The `data` prop should have the following structure:

```javascript
{
  labels: ['24000', '24100', '24200', ...], // X-axis labels (strike prices)
  callsOI: [1500000, 2300000, 1800000, ...], // Call options open interest values
  putsOI: [500000, 800000, 1200000, ...] // Put options open interest values
}
```

## Configuration

The `config` prop allows customization of the datasets:

```javascript
{
  datasets: [
    {
      label: 'Calls OI',
      darkColor: '#B74040',
      lightColor: '#EB4B4B',
      key: 'callsOI',
    },
    {
      label: 'Puts OI',
      darkColor: '#02A85D',
      lightColor: '#2CB079',
      key: 'putsOI',
    },
  ],
}
```

## Usage Examples

### Basic Usage
```jsx
<HighchartsBarChart
  data={oiData}
  height={240}
  yAxisFormatter={(value) => `${(value / 100000).toLocaleString()} Lacs`}
/>
```

### With Custom Scroll Position
```jsx
<HighchartsBarChart
  data={oiData}
  height={240}
  scrollToIndex={5} // Scroll to show index 5 in view
  yAxisFormatter={(value) => `${(value / 100000).toLocaleString()} Lacs`}
/>
```

### With Custom Configuration
```jsx
<HighchartsBarChart
  data={oiData}
  height={240}
  config={{
    datasets: [
      {
        label: 'Custom Calls',
        darkColor: '#FF0000',
        lightColor: '#FF6666',
        key: 'callsOI',
      },
      {
        label: 'Custom Puts',
        darkColor: '#00FF00',
        lightColor: '#66FF66',
        key: 'putsOI',
      },
    ],
  }}
  yAxisFormatter={(value) => `${value.toLocaleString()}`}
/>
```

## Integration with FOIndexAnalysisWidget

This component is integrated into the FOIndexAnalysisWidget as the "Change in OI 2" tab, providing an alternative visualization using Highcharts instead of Chart.js.

## Dependencies

- `highcharts`: Core Highcharts library
- `highcharts-react-official`: Official React wrapper for Highcharts
- React 16.2.0+

## Styling

The component uses SCSS modules for styling. The main styles are defined in `index.scss` and include:

- Responsive container styling
- Custom scrollbar styling for webkit browsers
- Dark/light theme support
- Empty state styling
- Highcharts override styles for consistent appearance

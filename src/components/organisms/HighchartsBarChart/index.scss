.highchartsContainer {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;

  // Ensure the Highcharts container takes full width and height
  > div {
    width: 100% !important;
    height: 100% !important;
  }

  // Style the Highcharts SVG container
  .highcharts-container {
    width: 100% !important;
    height: 100% !important;
  }

  // Style the scrollable plot area
  .highcharts-scrollable-plot-area {
    overflow-x: auto !important;
    overflow-y: hidden !important;

    // Custom scrollbar for the plot area
    &::-webkit-scrollbar {
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }

  // Dark mode scrollbar for plot area
  .dark-theme & {
    .highcharts-scrollable-plot-area {
      &::-webkit-scrollbar-thumb {
        background: rgba(238, 238, 238, 0.3);
      }

      &::-webkit-scrollbar-thumb:hover {
        background: rgba(238, 238, 238, 0.5);
      }
    }
  }
}

  // Custom scrollbar styling for webkit browsers
  ::-webkit-scrollbar {
    height: 4px;
    width: 4px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
  }

  // Dark mode scrollbar
  .dark-theme & {
    ::-webkit-scrollbar-thumb {
      background: rgba(238, 238, 238, 0.3);
    }

    ::-webkit-scrollbar-thumb:hover {
      background: rgba(238, 238, 238, 0.5);
    }
  }
}

.emptyChart {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  background: transparent;
  
  span {
    color: #999;
    font-size: 14px;
    font-weight: 400;
  }

  .dark-theme & {
    span {
      color: #666;
    }
  }
}

// Override Highcharts default styles
.highcharts-container {
  .highcharts-background {
    fill: transparent !important;
  }

  .highcharts-plot-background {
    fill: transparent !important;
  }

  // Ensure bars have proper spacing and appearance
  .highcharts-series-group {
    .highcharts-column-series {
      .highcharts-point {
        stroke-width: 0;
      }
    }
  }

  // Style the axis lines and labels
  .highcharts-axis-line {
    stroke: transparent !important;
  }

  .highcharts-tick {
    stroke: transparent !important;
  }

  .highcharts-grid-line {
    stroke: transparent !important;
  }

  // Ensure Y-axis stays fixed on the right
  .highcharts-yaxis {
    position: sticky !important;
    right: 0 !important;
    z-index: 10 !important;
  }

  // Style the scrollbar
  .highcharts-scrollbar {
    .highcharts-scrollbar-track {
      fill: transparent !important;
      stroke: transparent !important;
    }

    .highcharts-scrollbar-thumb {
      fill: rgba(255, 255, 255, 0.3) !important;
      stroke: transparent !important;
      rx: 4 !important;
    }

    .highcharts-scrollbar-button {
      fill: transparent !important;
      stroke: transparent !important;
    }

    .highcharts-scrollbar-rifles {
      stroke: transparent !important;
    }
  }

  // Dark theme scrollbar
  .dark-theme & {
    .highcharts-scrollbar {
      .highcharts-scrollbar-thumb {
        fill: rgba(238, 238, 238, 0.3) !important;
      }
    }
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    .highcharts-axis-labels {
      text {
        font-size: 10px !important;
      }
    }
  }
}

// Animation overrides to ensure smooth performance
.highcharts-container * {
  animation: none !important;
  transition: none !important;
}

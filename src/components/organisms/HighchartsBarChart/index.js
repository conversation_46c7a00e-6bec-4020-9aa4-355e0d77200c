import { useMemo, useRef, useEffect } from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { isDarkMode } from '../../../utils/commonUtil';
import { THEMES } from '../../../utils/enums';
import styles from './index.scss';

const HighchartsBarChart = ({
  data = null,
  height = 200,
  config = null,
  scrollToIndex = null, // Index to scroll to on mount
  yAxisFormatter = (value) => value,
}) => {
  const theme = isDarkMode() ? THEMES.DARK : THEMES.LIGHT;
  const chartRef = useRef(null);
  const currentData = data;

  // Default configuration for the chart
  const defaultConfig = {
    datasets: [
      {
        label: 'Calls OI',
        darkColor: '#B74040',
        lightColor: '#EB4B4B',
        key: 'callsOI',
      },
      {
        label: 'Puts OI',
        darkColor: '#02A85D',
        lightColor: '#2CB079',
        key: 'putsOI',
      },
    ],
  };

  const chartConfig = useMemo(
    () => ({
      ...defaultConfig,
      ...config,
    }),
    [config],
  );

  // Prepare series data for Highcharts
  const seriesData = useMemo(() => {
    if (!currentData || !currentData.labels) return [];

    return chartConfig.datasets.map((dataset) => ({
      name: dataset.label,
      data: currentData[dataset.key] || [],
      color: theme === THEMES.DARK ? dataset.darkColor : dataset.lightColor,
      borderRadius: 4,
    }));
  }, [currentData, theme, chartConfig]);

  // Highcharts options
  const chartOptions = useMemo(
    () => ({
      chart: {
        type: 'column',
        height,
        backgroundColor: 'transparent',
        spacing: [10, 10, 15, 10],
        animation: false,
        scrollablePlotArea: {
          minWidth: Math.max(800, (currentData?.labels?.length || 0) * 60), // Dynamic width based on data
          scrollPositionX: 1, // Start scrolled to the right (end)
        },
      },
      title: {
        text: null,
      },
      credits: {
        enabled: false,
      },
      legend: {
        enabled: false,
      },
      xAxis: {
        categories: currentData?.labels || [],
        labels: {
          style: {
            color: theme === THEMES.DARK ? '#EEEEEE8A' : '#1010108A',
            fontSize: '12px',
          },
        },
        lineColor: 'transparent',
        tickColor: 'transparent',
        gridLineWidth: 0,
      },
      yAxis: {
        title: {
          text: null,
        },
        labels: {
          style: {
            color: theme === THEMES.DARK ? '#EEEEEE8A' : '#1010108A',
            fontSize: '12px',
          },
          formatter() {
            return yAxisFormatter(this.value);
          },
        },
        opposite: true, // Sticky right Y-axis
        gridLineWidth: 0,
        lineWidth: 0,
        tickWidth: 0,
      },
      plotOptions: {
        column: {
          grouping: true,
          shadow: false,
          borderWidth: 0,
          pointPadding: 0.1,
          groupPadding: 0.1,
          borderRadius: 4,
        },
        series: {
          animation: false,
        },
      },
      tooltip: {
        enabled: false,
      },
      series: seriesData,
    }),
    [currentData, theme, height, seriesData, yAxisFormatter],
  );

  // Auto-scroll to specific index on mount
  useEffect(() => {
    if (chartRef.current && scrollToIndex !== null && currentData?.labels) {
      const { chart } = chartRef.current;
      if (
        chart &&
        scrollToIndex >= 0 &&
        scrollToIndex < currentData.labels.length
      ) {
        // Small delay to ensure chart is fully rendered
        setTimeout(() => {
          // Find the scrollable container
          const scrollableContainer = chart.container.querySelector(
            '.highcharts-scrollable-plot-area',
          );
          if (scrollableContainer) {
            const dataLength = currentData.labels.length;
            const scrollPercentage =
              scrollToIndex / Math.max(1, dataLength - 1);
            const maxScroll =
              scrollableContainer.scrollWidth - scrollableContainer.clientWidth;
            scrollableContainer.scrollLeft = maxScroll * scrollPercentage;
          }
        }, 300);
      }
    }
  }, [scrollToIndex, currentData]);

  // Auto-scroll to the end by default (similar to existing behavior)
  useEffect(() => {
    if (chartRef.current && scrollToIndex === null && currentData?.labels) {
      const { chart } = chartRef.current;
      if (chart && currentData.labels.length > 0) {
        // Small delay to ensure chart is fully rendered
        setTimeout(() => {
          // Find the scrollable container and scroll to the end
          const scrollableContainer = chart.container.querySelector(
            '.highcharts-scrollable-plot-area',
          );
          if (scrollableContainer) {
            scrollableContainer.scrollLeft =
              scrollableContainer.scrollWidth - scrollableContainer.clientWidth;
          }
        }, 300);
      }
    }
  }, [currentData, scrollToIndex]);

  if (!currentData || !currentData.labels || currentData.labels.length === 0) {
    return (
      <div className={styles.emptyChart} style={{ height: `${height}px` }}>
        <span>No data available</span>
      </div>
    );
  }

  return (
    <div className={styles.highchartsContainer}>
      <HighchartsReact
        highcharts={Highcharts}
        options={chartOptions}
        ref={chartRef}
      />
    </div>
  );
};

export default HighchartsBarChart;

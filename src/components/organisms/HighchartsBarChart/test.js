import React from 'react';
import HighchartsBarChart from './index';

// Test data in the same format as the existing OI data
const testData = {
  labels: [
    '24000',
    '24100',
    '24200',
    '24300',
    '24400',
    '24500',
    '24600',
    '24700',
    '24800',
    '24900',
    '25000',
    '25100',
    '25200',
  ],
  callsOI: [
    1500000, 2300000, 1800000, 2100000, 2800000, 3200000, 2900000, 2400000,
    1900000, 1600000, 1200000, 800000, 500000,
  ],
  putsOI: [
    500000, 800000, 1200000, 1600000, 1900000, 2400000, 2900000, 3200000,
    2800000, 2100000, 1800000, 2300000, 1500000,
  ],
};

const HighchartsBarChartTest = () => (
  <div
    style={{
      padding: '20px',
      backgroundColor: '#f5f5f5',
      minHeight: '100vh',
    }}
  >
    <h2>Highcharts Bar Chart Test</h2>

    <div style={{ marginBottom: '30px' }}>
      <h3>Default Chart (Auto-scroll to end)</h3>
      <div
        style={{
          height: '300px',
          backgroundColor: 'white',
          padding: '10px',
          borderRadius: '8px',
        }}
      >
        <HighchartsBarChart
          data={testData}
          height={280}
          yAxisFormatter={(value) =>
            `${(value / 100000).toLocaleString()} Lacs`
          }
        />
      </div>
    </div>

    <div style={{ marginBottom: '30px' }}>
      <h3>Chart with Scroll to Index 5</h3>
      <div
        style={{
          height: '300px',
          backgroundColor: 'white',
          padding: '10px',
          borderRadius: '8px',
        }}
      >
        <HighchartsBarChart
          data={testData}
          height={280}
          scrollToIndex={5}
          yAxisFormatter={(value) =>
            `${(value / 100000).toLocaleString()} Lacs`
          }
        />
      </div>
    </div>

    <div style={{ marginBottom: '30px' }}>
      <h3>Empty Data Chart</h3>
      <div
        style={{
          height: '300px',
          backgroundColor: 'white',
          padding: '10px',
          borderRadius: '8px',
        }}
      >
        <HighchartsBarChart
          data={{ labels: [], callsOI: [], putsOI: [] }}
          height={280}
          yAxisFormatter={(value) =>
            `${(value / 100000).toLocaleString()} Lacs`
          }
        />
      </div>
    </div>
  </div>
);

export default HighchartsBarChartTest;
